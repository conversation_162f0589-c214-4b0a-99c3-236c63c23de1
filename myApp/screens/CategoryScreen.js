import React from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
} from 'react-native';
import Header from '../components/Header';
import ServiceCard from '../components/ServiceCard';

export default function CategoryScreen({ route, navigation }) {
  const { category } = route.params;

  const services = [
    {
      id: 1,
      title: `${category.name} Service 1`,
      provider: 'Professional Services Co.',
      rating: 4.7,
      price: '$45/hour',
      description: `Professional ${category.name.toLowerCase()} services`,
    },
    {
      id: 2,
      title: `${category.name} Service 2`,
      provider: 'Expert Solutions',
      rating: 4.9,
      price: '$60/hour',
      description: `Premium ${category.name.toLowerCase()} services`,
    },
    {
      id: 3,
      title: `${category.name} Service 3`,
      provider: 'Quick Fix Services',
      rating: 4.5,
      price: '$40/hour',
      description: `Affordable ${category.name.toLowerCase()} services`,
    },
  ];

  const renderService = ({ item }) => (
    <View style={styles.serviceCardWrapper}>
      <ServiceCard
        service={item}
        onPress={() => navigation.navigate('Service', { service: item })}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <Header title={`${category.name} Services`} />

      <View style={styles.content}>
        <View style={styles.categoryHeader}>
          <View style={styles.iconCircle}>
            <Text style={styles.categoryIcon}>{category.icon}</Text>
          </View>
          <Text style={styles.categoryTitle}>{category.name}</Text>
          <Text style={styles.categorySubtitle}>
            Find the best {category.name.toLowerCase()} services near you
          </Text>
        </View>

        <FlatList
          data={services}
          renderItem={renderService}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.servicesList}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fafafa',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  categoryHeader: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 24,
    backgroundColor: '#fff',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 3,
  },
  iconCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  categoryIcon: {
    fontSize: 40,
  },
  categoryTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  categorySubtitle: {
    fontSize: 15,
    color: '#666',
    textAlign: 'center',
  },
  servicesList: {
    paddingBottom: 20,
  },
  serviceCardWrapper: {
    marginBottom: 12, // spacing between cards
  },
});
