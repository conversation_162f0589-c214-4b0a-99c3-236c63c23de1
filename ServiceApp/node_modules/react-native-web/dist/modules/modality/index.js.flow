/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import { addEventListener } from '../addEventListener';
import canUseDOM from '../canUseDom';
export type Modality = 'keyboard' | 'mouse' | 'touch' | 'pen';
declare var supportsPointerEvent: () => any;
let activeModality = 'keyboard';
let modality = 'keyboard';
let previousModality;
let previousActiveModality;
let isEmulatingMouseEvents = false;
const listeners = new Set();
const KEYBOARD = 'keyboard';
const MOUSE = 'mouse';
const TOUCH = 'touch';
const BLUR = 'blur';
const CONTEXTMENU = 'contextmenu';
const FOCUS = 'focus';
const KEYDOWN = 'keydown';
const MOUSEDOWN = 'mousedown';
const MOUSEMOVE = 'mousemove';
const MOUSEUP = 'mouseup';
const POINTERDOWN = 'pointerdown';
const POINTERMOVE = 'pointermove';
const SCROLL = 'scroll';
const SELECTIONCHANGE = 'selectionchange';
const TOUCHCANCEL = 'touchcancel';
const TOUCHMOVE = 'touchmove';
const TOUCHSTART = 'touchstart';
const VISIBILITYCHANGE = 'visibilitychange';
const bubbleOptions = {
  passive: true
};
const captureOptions = {
  capture: true,
  passive: true
};
declare function restoreModality(): any;
declare function onBlurWindow(): any;
declare function onFocusWindow(): any;
declare function onKeyDown(event: any): any;
declare function onVisibilityChange(): any;
declare function onPointerish(event: any): any;
if (canUseDOM) {
  // Window events
  addEventListener(window, BLUR, onBlurWindow, bubbleOptions);
  addEventListener(window, FOCUS, onFocusWindow, bubbleOptions);
  // Must be capture phase because 'stopPropagation' might prevent these
  // events bubbling to the document.
  addEventListener(document, KEYDOWN, onKeyDown, captureOptions);
  addEventListener(document, VISIBILITYCHANGE, onVisibilityChange, captureOptions);
  addEventListener(document, POINTERDOWN, onPointerish, captureOptions);
  addEventListener(document, POINTERMOVE, onPointerish, captureOptions);
  // Fallback events
  addEventListener(document, CONTEXTMENU, onPointerish, captureOptions);
  addEventListener(document, MOUSEDOWN, onPointerish, captureOptions);
  addEventListener(document, MOUSEMOVE, onPointerish, captureOptions);
  addEventListener(document, MOUSEUP, onPointerish, captureOptions);
  addEventListener(document, TOUCHCANCEL, onPointerish, captureOptions);
  addEventListener(document, TOUCHMOVE, onPointerish, captureOptions);
  addEventListener(document, TOUCHSTART, onPointerish, captureOptions);
  addEventListener(document, SELECTIONCHANGE, onPointerish, captureOptions);
  addEventListener(document, SCROLL, onPointerish, captureOptions);
}
declare function callListeners(): any;
declare export function getActiveModality(): Modality;
declare export function getModality(): Modality;
declare export function addModalityListener(listener: ({
  activeModality: Modality,
  modality: Modality,
}) => void): () => void;
declare export function testOnly_resetActiveModality(): any;