{"version": 3, "names": ["_reactNative", "require", "_react", "_interopRequireDefault", "e", "__esModule", "default", "_extends", "Object", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ScreenStackHeaderBackButtonImage", "props", "createElement", "View", "Image", "resizeMode", "fadeDuration", "exports", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackHeaderConfig.web.tsx"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAA0B,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAP,CAAA,MAAAA,CAAA,GAAAQ,SAAA,CAAAC,MAAA,EAAAT,CAAA,UAAAU,CAAA,GAAAF,SAAA,CAAAR,CAAA,YAAAW,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAJ,QAAA,CAAAW,KAAA,OAAAN,SAAA;AAGnB,MAAMO,gCAAgC,GAC3CC,KAAiB,iBAEjBlB,MAAA,CAAAI,OAAA,CAAAe,aAAA,CAACrB,YAAA,CAAAsB,IAAI,qBACHpB,MAAA,CAAAI,OAAA,CAAAe,aAAA,CAACrB,YAAA,CAAAuB,KAAK,EAAAhB,QAAA;EAACiB,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKL,KAAK,CAAG,CACpD,CACP;AAACM,OAAA,CAAAP,gCAAA,GAAAA,gCAAA;AAEK,MAAMQ,0BAA0B,GAAIP,KAAgB,iBACzDlB,MAAA,CAAAI,OAAA,CAAAe,aAAA,CAACrB,YAAA,CAAAsB,IAAI,EAAKF,KAAQ,CACnB;AAACM,OAAA,CAAAC,0BAAA,GAAAA,0BAAA;AAEK,MAAMC,yBAAyB,GAAIR,KAAgB,iBACxDlB,MAAA,CAAAI,OAAA,CAAAe,aAAA,CAACrB,YAAA,CAAAsB,IAAI,EAAKF,KAAQ,CACnB;AAACM,OAAA,CAAAE,yBAAA,GAAAA,yBAAA;AAEK,MAAMC,2BAA2B,GAAIT,KAAgB,iBAC1DlB,MAAA,CAAAI,OAAA,CAAAe,aAAA,CAACrB,YAAA,CAAAsB,IAAI,EAAKF,KAAQ,CACnB;AAACM,OAAA,CAAAG,2BAAA,GAAAA,2BAAA;AAEK,MAAMC,8BAA8B,GACzCV,KAAgB,iBACAlB,MAAA,CAAAI,OAAA,CAAAe,aAAA,CAACrB,YAAA,CAAAsB,IAAI,EAAKF,KAAQ,CAAC;AAACM,OAAA,CAAAI,8BAAA,GAAAA,8BAAA;AAE/B,MAAMC,uBAAuB,GAClCX,KAAmC,iBACnBlB,MAAA,CAAAI,OAAA,CAAAe,aAAA,CAACrB,YAAA,CAAAsB,IAAI,EAAKF,KAAQ,CAAC;AAACM,OAAA,CAAAK,uBAAA,GAAAA,uBAAA;AAE/B,MAAMC,wBAEZ,GAAAN,OAAA,CAAAM,wBAAA,GAAGV,iBAAI", "ignoreList": []}