{"name": "memoize-one", "version": "6.0.0", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/alexreardon/memoize-one.git"}, "files": ["/dist", "/src"], "size-limit": [{"path": "dist/memoize-one.min.js", "limit": "234B"}, {"path": "dist/memoize-one.js", "limit": "234B"}, {"path": "dist/memoize-one.cjs.js", "limit": "230B"}, {"path": "dist/memoize-one.esm.js", "limit": "246B"}], "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@size-limit/preset-small-lib": "^5.0.4", "@types/benchmark": "^2.1.1", "@types/jest": "^27.0.2", "@types/lodash.isequal": "^4.5.5", "@types/lodash.memoize": "^4.1.6", "@types/node": "^16.10.1", "@typescript-eslint/eslint-plugin": "^4.31.2", "@typescript-eslint/parser": "^4.31.2", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "eslint": "7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jest": "^24.4.2", "eslint-plugin-prettier": "^4.0.0", "expect-type": "^0.12.0", "fast-memoize": "^2.5.2", "jest": "^27.2.2", "lodash.isequal": "^4.5.0", "lodash.memoize": "^4.1.2", "markdown-table": "^3.0.1", "mem": "^9.0.1", "memoizee": "^0.4.15", "moize": "^6.1.0", "nanocolors": "^0.2.9", "ora": "^6.0.1", "prettier": "2.4.1", "rimraf": "3.0.2", "rollup": "^2.57.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript": "^1.0.1", "size-limit": "^5.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "tslib": "^2.3.1", "typescript": "^4.4.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn prettier:check && yarn eslint:check && yarn typescript:check", "test": "yarn jest", "test:size": "yarn build && yarn size-limit", "typescript:check": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "eslint:check": "eslint $npm_package_config_prettier_target", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "yarn rimraf dist", "build:dist": "yarn rollup -c", "build:typescript": "yarn tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "yarn ts-node ./benchmarks/shallow-equal.ts", "perf:library-comparison": "yarn build && node ./benchmarks/library-comparison.js", "prepublishOnly": "yarn build"}}