{"version": 3, "names": ["React", "BottomTabBarHeightContext", "useBottomTabBarHeight", "height", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useBottomTabBarHeight.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,yBAAyB,QAAQ,gCAA6B;AAEvE,OAAO,SAASC,qBAAqBA,CAAA,EAAG;EACtC,MAAMC,MAAM,GAAGH,KAAK,CAACI,UAAU,CAACH,yBAAyB,CAAC;EAE1D,IAAIE,MAAM,KAAKE,SAAS,EAAE;IACxB,MAAM,IAAIC,KAAK,CACb,2FACF,CAAC;EACH;EAEA,OAAOH,MAAM;AACf", "ignoreList": []}