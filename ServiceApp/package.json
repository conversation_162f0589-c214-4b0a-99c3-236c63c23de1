{"name": "serviceapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.1", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@supabase/supabase-js": "^2.50.0", "expo": "~53.0.10", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "^19.1.0", "react-native": "0.79.3", "react-native-gesture-handler": "^2.25.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}