{"version": 3, "names": ["getHeaderTitle", "Header", "SafeAreaProviderCompat", "Screen", "StackActions", "React", "Animated", "Platform", "StyleSheet", "SafeAreaInsetsContext", "FadeTransition", "ShiftTransition", "BottomTabBarHeightCallbackContext", "BottomTabBarHeightContext", "useAnimatedHashMap", "BottomTabBar", "getTabBarHeight", "MaybeScreen", "MaybeScreenContainer", "jsx", "_jsx", "jsxs", "_jsxs", "EPSILON", "STATE_INACTIVE", "STATE_TRANSITIONING_OR_BELOW_TOP", "STATE_ON_TOP", "NAMED_TRANSITIONS_PRESETS", "fade", "shift", "none", "sceneStyleInterpolator", "undefined", "transitionSpec", "animation", "config", "duration", "useNativeDriver", "OS", "hasAnimation", "options", "Boolean", "renderTabBarDefault", "props", "BottomTabView", "tabBar", "state", "navigation", "descriptors", "safeAreaInsets", "detachInactiveScreens", "focusedRouteKey", "routes", "index", "key", "loaded", "setLoaded", "useState", "includes", "previousRouteKeyRef", "useRef", "tabAnims", "useEffect", "previousRouteKey", "current", "popToTopAction", "popToTopOnBlur", "prevRoute", "find", "route", "type", "popToTop", "target", "animateToIndex", "emit", "parallel", "map", "spec", "toValue", "filter", "start", "finished", "dispatch", "dimensions", "initialMetrics", "frame", "tabBarHeight", "setTabBarHeight", "insets", "style", "tabBarStyle", "renderTabBar", "Consumer", "children", "top", "right", "bottom", "left", "hasTwoStates", "some", "tabBarPosition", "tabBarElement", "Provider", "value", "flexDirection", "enabled", "styles", "screens", "descriptor", "lazy", "isFocused", "isPreloaded", "preloadedRouteKeys", "freezeOnBlur", "header", "layout", "title", "name", "headerShown", "headerStatusBarHeight", "headerTransparent", "sceneStyle", "customSceneStyle", "progress", "animationEnabled", "activityState", "interpolate", "inputRange", "outputRange", "extrapolate", "absoluteFill", "zIndex", "active", "shouldFreeze", "focused", "render", "create", "flex", "overflow"], "sourceRoot": "../../../src", "sources": ["views/BottomTabView.tsx"], "mappings": ";;AAAA,SACEA,cAAc,EACdC,MAAM,EACNC,sBAAsB,EACtBC,MAAM,QACD,4BAA4B;AACnC,SAGEC,YAAY,QAEP,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAC7D,SAASC,qBAAqB,QAAQ,gCAAgC;AAEtE,SACEC,cAAc,EACdC,eAAe,QACV,2CAAwC;AAU/C,SAASC,iCAAiC,QAAQ,+CAA4C;AAC9F,SAASC,yBAAyB,QAAQ,uCAAoC;AAC9E,SAASC,kBAAkB,QAAQ,gCAA6B;AAChE,SAASC,YAAY,EAAEC,eAAe,QAAQ,mBAAgB;AAC9D,SAASC,WAAW,EAAEC,oBAAoB,QAAQ,qBAAkB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAQrE,MAAMC,OAAO,GAAG,IAAI;AACpB,MAAMC,cAAc,GAAG,CAAC;AACxB,MAAMC,gCAAgC,GAAG,CAAC;AAC1C,MAAMC,YAAY,GAAG,CAAC;AAEtB,MAAMC,yBAAyB,GAAG;EAChCC,IAAI,EAAElB,cAAc;EACpBmB,KAAK,EAAElB,eAAe;EACtBmB,IAAI,EAAE;IACJC,sBAAsB,EAAEC,SAAS;IACjCC,cAAc,EAAE;MACdC,SAAS,EAAE,QAAQ;MACnBC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAE;IACxB;EACF;AACF,CAAU;AAEV,MAAMC,eAAe,GAAG9B,QAAQ,CAAC+B,EAAE,KAAK,KAAK;AAE7C,MAAMC,YAAY,GAAIC,OAAmC,IAAK;EAC5D,MAAM;IAAEN,SAAS;IAAED;EAAe,CAAC,GAAGO,OAAO;EAE7C,IAAIN,SAAS,EAAE;IACb,OAAOA,SAAS,KAAK,MAAM;EAC7B;EAEA,OAAOO,OAAO,CAACR,cAAc,CAAC;AAChC,CAAC;AAED,MAAMS,mBAAmB,GAAIC,KAAwB,iBACnDvB,IAAA,CAACL,YAAY;EAAA,GAAK4B;AAAK,CAAG,CAC3B;AAED,OAAO,SAASC,aAAaA,CAACD,KAAY,EAAE;EAC1C,MAAM;IACJE,MAAM,GAAGH,mBAAmB;IAC5BI,KAAK;IACLC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,qBAAqB,GAAG3C,QAAQ,CAAC+B,EAAE,KAAK,KAAK,IAC3C/B,QAAQ,CAAC+B,EAAE,KAAK,SAAS,IACzB/B,QAAQ,CAAC+B,EAAE,KAAK;EACpB,CAAC,GAAGK,KAAK;EAET,MAAMQ,eAAe,GAAGL,KAAK,CAACM,MAAM,CAACN,KAAK,CAACO,KAAK,CAAC,CAACC,GAAG;;EAErD;AACF;AACA;EACE,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnD,KAAK,CAACoD,QAAQ,CAAC,CAACN,eAAe,CAAC,CAAC;EAE7D,IAAI,CAACI,MAAM,CAACG,QAAQ,CAACP,eAAe,CAAC,EAAE;IACrC;IACAK,SAAS,CAAC,CAAC,GAAGD,MAAM,EAAEJ,eAAe,CAAC,CAAC;EACzC;EAEA,MAAMQ,mBAAmB,GAAGtD,KAAK,CAACuD,MAAM,CAACT,eAAe,CAAC;EACzD,MAAMU,QAAQ,GAAG/C,kBAAkB,CAACgC,KAAK,CAAC;EAE1CzC,KAAK,CAACyD,SAAS,CAAC,MAAM;IACpB,MAAMC,gBAAgB,GAAGJ,mBAAmB,CAACK,OAAO;IAEpD,IAAIC,cAA4C;IAEhD,IACEF,gBAAgB,KAAKZ,eAAe,IACpCH,WAAW,CAACe,gBAAgB,CAAC,EAAEvB,OAAO,CAAC0B,cAAc,EACrD;MACA,MAAMC,SAAS,GAAGrB,KAAK,CAACM,MAAM,CAACgB,IAAI,CAChCC,KAAK,IAAKA,KAAK,CAACf,GAAG,KAAKS,gBAC3B,CAAC;MAED,IAAII,SAAS,EAAErB,KAAK,EAAEwB,IAAI,KAAK,OAAO,IAAIH,SAAS,CAACrB,KAAK,CAACQ,GAAG,EAAE;QAC7DW,cAAc,GAAG;UACf,GAAG7D,YAAY,CAACmE,QAAQ,CAAC,CAAC;UAC1BC,MAAM,EAAEL,SAAS,CAACrB,KAAK,CAACQ;QAC1B,CAAC;MACH;IACF;IAEA,MAAMmB,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIV,gBAAgB,KAAKZ,eAAe,EAAE;QACxCJ,UAAU,CAAC2B,IAAI,CAAC;UACdJ,IAAI,EAAE,iBAAiB;UACvBE,MAAM,EAAErB;QACV,CAAC,CAAC;MACJ;MAEA7C,QAAQ,CAACqE,QAAQ,CACf7B,KAAK,CAACM,MAAM,CACTwB,GAAG,CAAC,CAACP,KAAK,EAAEhB,KAAK,KAAK;QACrB,MAAM;UAAEb;QAAQ,CAAC,GAAGQ,WAAW,CAACqB,KAAK,CAACf,GAAG,CAAC;QAC1C,MAAM;UACJpB,SAAS,GAAG,MAAM;UAClBD,cAAc,GAAGN,yBAAyB,CAACO,SAAS,CAAC,CAClDD;QACL,CAAC,GAAGO,OAAO;QAEX,IAAIqC,IAAI,GAAG5C,cAAc;QAEzB,IACEoC,KAAK,CAACf,GAAG,KAAKS,gBAAgB,IAC9BM,KAAK,CAACf,GAAG,KAAKH,eAAe,EAC7B;UACA;UACA;UACA0B,IAAI,GAAGlD,yBAAyB,CAACG,IAAI,CAACG,cAAc;QACtD;QAEA4C,IAAI,GAAGA,IAAI,IAAIlD,yBAAyB,CAACG,IAAI,CAACG,cAAc;QAE5D,MAAM6C,OAAO,GACXzB,KAAK,KAAKP,KAAK,CAACO,KAAK,GAAG,CAAC,GAAGA,KAAK,IAAIP,KAAK,CAACO,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3D,OAAO/C,QAAQ,CAACuE,IAAI,CAAC3C,SAAS,CAAC,CAAC2B,QAAQ,CAACQ,KAAK,CAACf,GAAG,CAAC,EAAE;UACnD,GAAGuB,IAAI,CAAC1C,MAAM;UACd2C,OAAO;UACPzC;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,CACD0C,MAAM,CAACtC,OAAO,CACnB,CAAC,CAACuC,KAAK,CAAC,CAAC;QAAEC;MAAS,CAAC,KAAK;QACxB,IAAIA,QAAQ,IAAIhB,cAAc,EAAE;UAC9BlB,UAAU,CAACmC,QAAQ,CAACjB,cAAc,CAAC;QACrC;QAEA,IAAIF,gBAAgB,KAAKZ,eAAe,EAAE;UACxCJ,UAAU,CAAC2B,IAAI,CAAC;YACdJ,IAAI,EAAE,eAAe;YACrBE,MAAM,EAAErB;UACV,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAEDsB,cAAc,CAAC,CAAC;IAEhBd,mBAAmB,CAACK,OAAO,GAAGb,eAAe;EAC/C,CAAC,EAAE,CACDH,WAAW,EACXG,eAAe,EACfJ,UAAU,EACVD,KAAK,CAACO,KAAK,EACXP,KAAK,CAACM,MAAM,EACZS,QAAQ,CACT,CAAC;EAEF,MAAMsB,UAAU,GAAGjF,sBAAsB,CAACkF,cAAc,CAACC,KAAK;EAC9D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlF,KAAK,CAACoD,QAAQ,CAAC,MACrDzC,eAAe,CAAC;IACd8B,KAAK;IACLE,WAAW;IACXmC,UAAU;IACVK,MAAM,EAAE;MACN,GAAGtF,sBAAsB,CAACkF,cAAc,CAACI,MAAM;MAC/C,GAAG7C,KAAK,CAACM;IACX,CAAC;IACDwC,KAAK,EAAEzC,WAAW,CAACF,KAAK,CAACM,MAAM,CAACN,KAAK,CAACO,KAAK,CAAC,CAACC,GAAG,CAAC,CAACd,OAAO,CAACkD;EAC5D,CAAC,CACH,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,oBACEvE,IAAA,CAACX,qBAAqB,CAACmF,QAAQ;MAAAC,QAAA,EAC3BL,MAAM,IACN3C,MAAM,CAAC;QACLC,KAAK,EAAEA,KAAK;QACZE,WAAW,EAAEA,WAAW;QACxBD,UAAU,EAAEA,UAAU;QACtByC,MAAM,EAAE;UACNM,GAAG,EAAE7C,cAAc,EAAE6C,GAAG,IAAIN,MAAM,EAAEM,GAAG,IAAI,CAAC;UAC5CC,KAAK,EAAE9C,cAAc,EAAE8C,KAAK,IAAIP,MAAM,EAAEO,KAAK,IAAI,CAAC;UAClDC,MAAM,EAAE/C,cAAc,EAAE+C,MAAM,IAAIR,MAAM,EAAEQ,MAAM,IAAI,CAAC;UACrDC,IAAI,EAAEhD,cAAc,EAAEgD,IAAI,IAAIT,MAAM,EAAES,IAAI,IAAI;QAChD;MACF,CAAC;IAAC,CAE0B,CAAC;EAErC,CAAC;EAED,MAAM;IAAE7C;EAAO,CAAC,GAAGN,KAAK;;EAExB;EACA,MAAMoD,YAAY,GAAG,CAAC9C,MAAM,CAAC+C,IAAI,CAAE9B,KAAK,IACtC9B,YAAY,CAACS,WAAW,CAACqB,KAAK,CAACf,GAAG,CAAC,CAACd,OAAO,CAC7C,CAAC;EAED,MAAM;IAAE4D,cAAc,GAAG;EAAS,CAAC,GAAGpD,WAAW,CAACG,eAAe,CAAC,CAACX,OAAO;EAE1E,MAAM6D,aAAa,gBACjBjF,IAAA,CAACR,iCAAiC,CAAC0F,QAAQ;IAEzCC,KAAK,EAAEhB,eAAgB;IAAAM,QAAA,EAEtBF,YAAY,CAAC;EAAC,GAHX,QAIsC,CAC7C;EAED,oBACErE,KAAA,CAACpB,sBAAsB;IACrBuF,KAAK,EAAE;MACLe,aAAa,EACXJ,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,OAAO,GACnD,KAAK,GACL;IACR,CAAE;IAAAP,QAAA,GAEDO,cAAc,KAAK,KAAK,IAAIA,cAAc,KAAK,MAAM,GAClDC,aAAa,GACb,IAAI,eACRjF,IAAA,CAACF,oBAAoB;MAEnBuF,OAAO,EAAEvD,qBAAsB;MAC/BgD,YAAY,EAAEA,YAAa;MAC3BT,KAAK,EAAEiB,MAAM,CAACC,OAAQ;MAAAd,QAAA,EAErBzC,MAAM,CAACwB,GAAG,CAAC,CAACP,KAAK,EAAEhB,KAAK,KAAK;QAC5B,MAAMuD,UAAU,GAAG5D,WAAW,CAACqB,KAAK,CAACf,GAAG,CAAC;QACzC,MAAM;UACJuD,IAAI,GAAG,IAAI;UACX3E,SAAS,GAAG,MAAM;UAClBH,sBAAsB,GAAGJ,yBAAyB,CAACO,SAAS,CAAC,CAC1DH;QACL,CAAC,GAAG6E,UAAU,CAACpE,OAAO;QACtB,MAAMsE,SAAS,GAAGhE,KAAK,CAACO,KAAK,KAAKA,KAAK;QACvC,MAAM0D,WAAW,GAAGjE,KAAK,CAACkE,kBAAkB,CAACtD,QAAQ,CAACW,KAAK,CAACf,GAAG,CAAC;QAEhE,IACEuD,IAAI,IACJ,CAACtD,MAAM,CAACG,QAAQ,CAACW,KAAK,CAACf,GAAG,CAAC,IAC3B,CAACwD,SAAS,IACV,CAACC,WAAW,EACZ;UACA;UACA,OAAO,IAAI;QACb;QAEA,MAAM;UACJE,YAAY;UACZC,MAAM,GAAGA,CAAC;YAAEC,MAAM;YAAE3E;UAA8B,CAAC,kBACjDpB,IAAA,CAACnB,MAAM;YAAA,GACDuC,OAAO;YACX2E,MAAM,EAAEA,MAAO;YACfC,KAAK,EAAEpH,cAAc,CAACwC,OAAO,EAAE6B,KAAK,CAACgD,IAAI;UAAE,CAC5C,CACF;UACDC,WAAW;UACXC,qBAAqB;UACrBC,iBAAiB;UACjBC,UAAU,EAAEC;QACd,CAAC,GAAGd,UAAU,CAACpE,OAAO;QAEtB,MAAM;UAAEiF;QAAW,CAAC,GAClB1F,sBAAsB,GAAG;UACvBiC,OAAO,EAAE;YACP2D,QAAQ,EAAE9D,QAAQ,CAACQ,KAAK,CAACf,GAAG;UAC9B;QACF,CAAC,CAAC,IAAI,CAAC,CAAC;QAEV,MAAMsE,gBAAgB,GAAGrF,YAAY,CAACqE,UAAU,CAACpE,OAAO,CAAC;QACzD,MAAMqF,aAAa,GAAGf,SAAS,GAC3BpF,YAAY,CAAC;QAAA,EACbkG,gBAAgB,CAAC;QAAA,EACf/D,QAAQ,CAACQ,KAAK,CAACf,GAAG,CAAC,CAACwE,WAAW,CAAC;UAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,GAAGxG,OAAO,EAAE,CAAC,CAAC;UAC/ByG,WAAW,EAAE,CACXvG,gCAAgC;UAAE;UAClCA,gCAAgC,EAChCD,cAAc,CAAE;UAAA,CACjB;UACDyG,WAAW,EAAE;QACf,CAAC,CAAC,GACFzG,cAAc;QAEpB,oBACEJ,IAAA,CAACH,WAAW;UAEVwE,KAAK,EAAE,CAACjF,UAAU,CAAC0H,YAAY,EAAE;YAAEC,MAAM,EAAErB,SAAS,GAAG,CAAC,GAAG,CAAC;UAAE,CAAC,CAAE;UACjEsB,MAAM,EAAEP,aAAc;UACtBpB,OAAO,EAAEvD,qBAAsB;UAC/B+D,YAAY,EAAEA,YAAa;UAC3BoB,YAAY,EAAER,aAAa,KAAKrG,cAAc,IAAI,CAACuF,WAAY;UAAAlB,QAAA,eAE/DzE,IAAA,CAACP,yBAAyB,CAACyF,QAAQ;YACjCC,KAAK,EAAEH,cAAc,KAAK,QAAQ,GAAGd,YAAY,GAAG,CAAE;YAAAO,QAAA,eAEtDzE,IAAA,CAACjB,MAAM;cACLmI,OAAO,EAAExB,SAAU;cACnBzC,KAAK,EAAEuC,UAAU,CAACvC,KAAM;cACxBtB,UAAU,EAAE6D,UAAU,CAAC7D,UAAW;cAClCuE,WAAW,EAAEA,WAAY;cACzBC,qBAAqB,EAAEA,qBAAsB;cAC7CC,iBAAiB,EAAEA,iBAAkB;cACrCN,MAAM,EAAEA,MAAM,CAAC;gBACbC,MAAM,EAAEhC,UAAU;gBAClBd,KAAK,EAAEuC,UAAU,CAACvC,KAAK;gBACvBtB,UAAU,EACR6D,UAAU,CAAC7D,UAAoD;gBACjEP,OAAO,EAAEoE,UAAU,CAACpE;cACtB,CAAC,CAAE;cACHiD,KAAK,EAAE,CAACiC,gBAAgB,EAAEE,gBAAgB,IAAIH,UAAU,CAAE;cAAA5B,QAAA,EAEzDe,UAAU,CAAC2B,MAAM,CAAC;YAAC,CACd;UAAC,CACyB;QAAC,GA5BhClE,KAAK,CAACf,GA6BA,CAAC;MAElB,CAAC;IAAC,GAhGE,SAiGgB,CAAC,EACtB8C,cAAc,KAAK,QAAQ,IAAIA,cAAc,KAAK,OAAO,GACtDC,aAAa,GACb,IAAI;EAAA,CACc,CAAC;AAE7B;AAEA,MAAMK,MAAM,GAAGlG,UAAU,CAACgI,MAAM,CAAC;EAC/B7B,OAAO,EAAE;IACP8B,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}