/// <reference types="react-native/types/modules/Codegen" />
import type { ViewProps } from 'react-native';
import { WithDefault } from 'react-native/Libraries/Types/CodegenTypes';
export interface NativeProps extends ViewProps {
    accessibilityContainerViewIsModal?: WithDefault<boolean, true>;
}
declare const _default: import("react-native/Libraries/Utilities/codegenNativeComponent").NativeComponentType<NativeProps>;
export default _default;
//# sourceMappingURL=FullWindowOverlayNativeComponent.d.ts.map