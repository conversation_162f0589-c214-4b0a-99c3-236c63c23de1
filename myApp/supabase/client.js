// API Base URL - Replace with your backend API endpoint
const API_BASE_URL = 'YOUR_API_BASE_URL'; // e.g., 'https://your-api.com/api/v1'

// API Client for mobile app communication with your backend
class ApiClient {
  constructor(baseURL) {
    this.baseURL = baseURL;
    this.token = null;
  }

  setAuthToken(token) {
    this.token = token;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body);
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return { data, error: null };
    } catch (error) {
      return { data: null, error: error.message };
    }
  }

  // Auth API calls
  async signUp(email, password, userData = {}) {
    return this.request('/auth/signup', {
      method: 'POST',
      body: { email, password, ...userData },
    });
  }

  async signIn(email, password) {
    return this.request('/auth/signin', {
      method: 'POST',
      body: { email, password },
    });
  }

  async signOut() {
    const result = await this.request('/auth/signout', {
      method: 'POST',
    });
    this.token = null;
    return result;
  }

  async getCurrentUser() {
    return this.request('/auth/me');
  }

  async refreshToken() {
    return this.request('/auth/refresh', {
      method: 'POST',
    });
  }

  // Services API calls
  async getServices(filters = {}) {
    const queryParams = new URLSearchParams(filters).toString();
    return this.request(`/services${queryParams ? `?${queryParams}` : ''}`);
  }

  async getServiceById(serviceId) {
    return this.request(`/services/${serviceId}`);
  }

  async getServicesByCategory(categoryId) {
    return this.request(`/services/category/${categoryId}`);
  }

  async searchServices(query, location = null) {
    return this.request('/services/search', {
      method: 'POST',
      body: { query, location },
    });
  }

  // Bookings API calls
  async createBooking(bookingData) {
    return this.request('/bookings', {
      method: 'POST',
      body: bookingData,
    });
  }

  async getUserBookings() {
    return this.request('/bookings/user');
  }

  async getBookingById(bookingId) {
    return this.request(`/bookings/${bookingId}`);
  }

  async updateBooking(bookingId, updateData) {
    return this.request(`/bookings/${bookingId}`, {
      method: 'PUT',
      body: updateData,
    });
  }

  async cancelBooking(bookingId) {
    return this.request(`/bookings/${bookingId}/cancel`, {
      method: 'POST',
    });
  }

  // Categories API calls
  async getCategories() {
    return this.request('/categories');
  }

  // User Profile API calls
  async updateProfile(profileData) {
    return this.request('/user/profile', {
      method: 'PUT',
      body: profileData,
    });
  }

  async uploadProfileImage(imageData) {
    const formData = new FormData();
    formData.append('image', imageData);

    return this.request('/user/profile/image', {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      body: formData,
    });
  }

  // Location/Mapping API calls (for future mapping integration)
  async searchServicesByLocation(latitude, longitude, radius = 10, serviceType = null) {
    return this.request('/services/location', {
      method: 'POST',
      body: { latitude, longitude, radius, serviceType },
    });
  }

  async getServiceProviderLocation(providerId) {
    return this.request(`/providers/${providerId}/location`);
  }

  async updateUserLocation(latitude, longitude, address = null) {
    return this.request('/user/location', {
      method: 'PUT',
      body: { latitude, longitude, address },
    });
  }

  // Reviews and Ratings API calls
  async getServiceReviews(serviceId) {
    return this.request(`/services/${serviceId}/reviews`);
  }

  async createReview(serviceId, rating, comment) {
    return this.request(`/services/${serviceId}/reviews`, {
      method: 'POST',
      body: { rating, comment },
    });
  }

  // Notifications API calls
  async getNotifications() {
    return this.request('/notifications');
  }

  async markNotificationAsRead(notificationId) {
    return this.request(`/notifications/${notificationId}/read`, {
      method: 'POST',
    });
  }
}

// Create and export API client instance
export const apiClient = new ApiClient(API_BASE_URL);
