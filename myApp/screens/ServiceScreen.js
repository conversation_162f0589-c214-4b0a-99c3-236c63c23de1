import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';
import Header from '../components/Header';

export default function ServiceScreen({ route, navigation }) {
  const { service } = route.params;

  return (
    <View style={styles.container}>
      <Header title="Service Details" />

      <ScrollView style={styles.content}>
        <View style={styles.serviceHeader}>
          {service.imageUrl ? (
            <Image
              source={{ uri: service.imageUrl }}
              style={styles.serviceImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.serviceImagePlaceholder}>
              <Text style={styles.imagePlaceholderText}>Service Image</Text>
            </View>
          )}

          <Text style={styles.serviceTitle}>{service.title}</Text>
          <Text style={styles.providerName}>{service.provider}</Text>

          <View style={styles.ratingContainer}>
            <Text style={styles.rating}>
              ⭐ {service.rating ?? 'N/A'}
            </Text>
            <Text style={styles.price}>
              {service.price ?? 'Contact for price'}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.description}>
            {service.description ||
              'Professional service with experienced technicians. We provide high-quality work with satisfaction guarantee.'}
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>What's Included</Text>
          <Text style={styles.bulletPoint}>• Professional consultation</Text>
          <Text style={styles.bulletPoint}>• Quality materials and tools</Text>
          <Text style={styles.bulletPoint}>• Clean-up after service</Text>
          <Text style={styles.bulletPoint}>• 30-day satisfaction guarantee</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Provider Information</Text>
          <Text style={styles.providerInfo}>
            Verified professional with 5+ years of experience
          </Text>
          <Text style={styles.providerInfo}>
            Licensed and insured
          </Text>
          <Text style={styles.providerInfo}>
            Available 7 days a week
          </Text>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.bookButton}
          onPress={() => navigation.navigate('Booking', { service })}
        >
          <Text style={styles.bookButtonText}>Book Now</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  serviceHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  serviceImage: {
    width: '100%',
    height: 200,
    borderRadius: 10,
    marginBottom: 15,
  },
  serviceImagePlaceholder: {
    width: '100%',
    height: 200,
    backgroundColor: '#f0f0f0',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  imagePlaceholderText: {
    color: '#999',
    fontSize: 16,
  },
  serviceTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 5,
    color: '#333',
  },
  providerName: {
    fontSize: 18,
    color: '#666',
    marginBottom: 10,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 20,
  },
  rating: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    color: '#444',
  },
  bulletPoint: {
    fontSize: 16,
    marginBottom: 5,
    color: '#444',
  },
  providerInfo: {
    fontSize: 16,
    marginBottom: 5,
    color: '#666',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  bookButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  bookButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
