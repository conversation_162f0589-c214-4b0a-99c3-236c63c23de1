{"version": 3, "names": ["React", "View", "CSSTransitions", "WebkitTransition", "Transition", "MozTransition", "MSTransition", "OTransition", "NativeSafeAreaProvider", "children", "style", "onInsetsChange", "useEffect", "document", "element", "createContextElement", "body", "append<PERSON><PERSON><PERSON>", "onEnd", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "window", "getComputedStyle", "insets", "top", "parseInt", "bottom", "left", "right", "frame", "x", "y", "width", "documentElement", "offsetWidth", "height", "offsetHeight", "nativeEvent", "addEventListener", "getSupportedTransitionEvent", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "createElement", "_supportedTransitionEvent", "key", "undefined", "_supportedEnv", "getSupportedEnv", "CSS", "supports", "getInset", "side", "position", "zIndex", "overflow", "visibility", "transitionDuration", "transitionProperty", "transitionDelay"], "sourceRoot": "../../src", "sources": ["NativeSafeAreaProvider.web.tsx"], "mappings": "AAAA;;AAEA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,cAAc;AAGnC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,cAAsC,GAAG;EAC7CC,gBAAgB,EAAE,qBAAqB;EACvCC,UAAU,EAAE,eAAe;EAC3BC,aAAa,EAAE,eAAe;EAC9BC,YAAY,EAAE,iBAAiB;EAC/BC,WAAW,EAAE;AACf,CAAC;AAED,OAAO,SAASC,sBAAsBA,CAAC;EACrCC,QAAQ;EACRC,KAAK;EACLC;AAC2B,CAAC,EAAE;EAC9BX,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACnC;IACF;IAEA,MAAMC,OAAO,GAAGC,oBAAoB,CAAC,CAAC;IACtCF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACH,OAAO,CAAC;IAClC,MAAMI,KAAK,GAAGA,CAAA,KAAM;MAClB,MAAM;QAAEC,UAAU;QAAEC,aAAa;QAAEC,WAAW;QAAEC;MAAa,CAAC,GAC5DC,MAAM,CAACC,gBAAgB,CAACV,OAAO,CAAC;MAElC,MAAMW,MAAM,GAAG;QACbC,GAAG,EAAEP,UAAU,GAAGQ,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC;QAC9CS,MAAM,EAAER,aAAa,GAAGO,QAAQ,CAACP,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC;QACvDS,IAAI,EAAER,WAAW,GAAGM,QAAQ,CAACN,WAAW,EAAE,EAAE,CAAC,GAAG,CAAC;QACjDS,KAAK,EAAER,YAAY,GAAGK,QAAQ,CAACL,YAAY,EAAE,EAAE,CAAC,GAAG;MACrD,CAAC;MACD,MAAMS,KAAK,GAAG;QACZC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,KAAK,EAAErB,QAAQ,CAACsB,eAAe,CAACC,WAAW;QAC3CC,MAAM,EAAExB,QAAQ,CAACsB,eAAe,CAACG;MACnC,CAAC;MACD;MACA3B,cAAc,CAAC;QAAE4B,WAAW,EAAE;UAAEd,MAAM;UAAEM;QAAM;MAAE,CAAC,CAAC;IACpD,CAAC;IACDjB,OAAO,CAAC0B,gBAAgB,CAACC,2BAA2B,CAAC,CAAC,EAAEvB,KAAK,CAAC;IAC9DA,KAAK,CAAC,CAAC;IACP,OAAO,MAAM;MACXL,QAAQ,CAACG,IAAI,CAAC0B,WAAW,CAAC5B,OAAO,CAAC;MAClCA,OAAO,CAAC6B,mBAAmB,CAACF,2BAA2B,CAAC,CAAC,EAAEvB,KAAK,CAAC;IACnE,CAAC;EACH,CAAC,EAAE,CAACP,cAAc,CAAC,CAAC;EAEpB,oBAAOX,KAAA,CAAA4C,aAAA,CAAC3C,IAAI;IAACS,KAAK,EAAEA;EAAM,GAAED,QAAe,CAAC;AAC9C;AAEA,IAAIoC,yBAAoD,GAAG,IAAI;AAC/D,SAASJ,2BAA2BA,CAAA,EAAW;EAC7C,IAAII,yBAAyB,IAAI,IAAI,EAAE;IACrC,OAAOA,yBAAyB;EAClC;EACA,MAAM/B,OAAO,GAAGD,QAAQ,CAAC+B,aAAa,CAAC,aAAa,CAAC;EAErDC,yBAAyB,GAAG3C,cAAc,CAACE,UAAU;EACrD,KAAK,MAAM0C,GAAG,IAAI5C,cAAc,EAAE;IAChC,IAAIY,OAAO,CAACJ,KAAK,CAACoC,GAAG,CAA8B,KAAKC,SAAS,EAAE;MACjEF,yBAAyB,GAAG3C,cAAc,CAAC4C,GAAG,CAAC;MAC/C;IACF;EACF;EACA,OAAOD,yBAAyB;AAClC;AAIA,IAAIG,aAA4B,GAAG,IAAI;AACvC,SAASC,eAAeA,CAAA,EAAW;EACjC,IAAID,aAAa,KAAK,IAAI,EAAE;IAC1B,OAAOA,aAAa;EACtB;EACA,MAAM;IAAEE;EAAI,CAAC,GAAG3B,MAAM;EACtB,IACE2B,GAAG,IACHA,GAAG,CAACC,QAAQ,IACZD,GAAG,CAACC,QAAQ,CAAC,oCAAoC,CAAC,EAClD;IACAH,aAAa,GAAG,UAAU;EAC5B,CAAC,MAAM;IACLA,aAAa,GAAG,KAAK;EACvB;EACA,OAAOA,aAAa;AACtB;AAEA,SAASI,QAAQA,CAACC,IAAY,EAAU;EACtC,OAAO,GAAGJ,eAAe,CAAC,CAAC,oBAAoBI,IAAI,GAAG;AACxD;AAEA,SAAStC,oBAAoBA,CAAA,EAAgB;EAC3C,MAAMD,OAAO,GAAGD,QAAQ,CAAC+B,aAAa,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAElC;EAAM,CAAC,GAAGI,OAAO;EACzBJ,KAAK,CAAC4C,QAAQ,GAAG,OAAO;EACxB5C,KAAK,CAACmB,IAAI,GAAG,GAAG;EAChBnB,KAAK,CAACgB,GAAG,GAAG,GAAG;EACfhB,KAAK,CAACwB,KAAK,GAAG,GAAG;EACjBxB,KAAK,CAAC2B,MAAM,GAAG,GAAG;EAClB3B,KAAK,CAAC6C,MAAM,GAAG,IAAI;EACnB7C,KAAK,CAAC8C,QAAQ,GAAG,QAAQ;EACzB9C,KAAK,CAAC+C,UAAU,GAAG,QAAQ;EAC3B;EACA/C,KAAK,CAACgD,kBAAkB,GAAG,OAAO;EAClChD,KAAK,CAACiD,kBAAkB,GAAG,SAAS;EACpCjD,KAAK,CAACkD,eAAe,GAAG,IAAI;EAC5BlD,KAAK,CAACS,UAAU,GAAGiC,QAAQ,CAAC,KAAK,CAAC;EAClC1C,KAAK,CAACU,aAAa,GAAGgC,QAAQ,CAAC,QAAQ,CAAC;EACxC1C,KAAK,CAACW,WAAW,GAAG+B,QAAQ,CAAC,MAAM,CAAC;EACpC1C,KAAK,CAACY,YAAY,GAAG8B,QAAQ,CAAC,OAAO,CAAC;EACtC,OAAOtC,OAAO;AAChB", "ignoreList": []}