/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 */

'use client';

import type { ColorValue } from '../../types';
import type { Props as TouchableWithoutFeedbackProps } from '../TouchableWithoutFeedback';
import type { ViewProps } from '../View';
import * as React from 'react';
import { useCallback, useMemo, useState, useRef } from 'react';
import useMergeRefs from '../../modules/useMergeRefs';
import usePressEvents from '../../modules/usePressEvents';
import StyleSheet from '../StyleSheet';
import View from '../View';
//import { warnOnce } from '../../modules/warnOnce';

type ViewStyle = $PropertyType<ViewProps, 'style'>;
type Props = $ReadOnly<{|
  ...TouchableWithoutFeedbackProps,
  activeOpacity?: ?number,
  onHideUnderlay?: ?() => void,
  onShowUnderlay?: ?() => void,
  style?: ViewStyle,
  testOnly_pressed?: ?boolean,
  underlayColor?: ?ColorValue,
|}>;
type ExtraStyles = $ReadOnly<{|
  child: ViewStyle,
  underlay: ViewStyle,
|}>;
declare function createExtraStyles(activeOpacity: any, underlayColor: any): ExtraStyles;
declare function hasPressHandler(props: any): boolean;
/**
 * A wrapper for making views respond properly to touches.
 * On press down, the opacity of the wrapped view is decreased, which allows
 * the underlay color to show through, darkening or tinting the view.
 *
 * The underlay comes from wrapping the child in a new View, which can affect
 * layout, and sometimes cause unwanted visual artifacts if not used correctly,
 * for example if the backgroundColor of the wrapped view isn't explicitly set
 * to an opaque color.
 *
 * TouchableHighlight must have one child (not zero or more than one).
 * If you wish to have several child components, wrap them in a View.
 */
declare function TouchableHighlight(props: Props, forwardedRef: any): React.Node;
const styles = StyleSheet.create({
  root: {
    userSelect: 'none'
  },
  actionable: {
    cursor: 'pointer',
    touchAction: 'manipulation'
  }
});
const MemoedTouchableHighlight = React.memo(React.forwardRef(TouchableHighlight));
MemoedTouchableHighlight.displayName = 'TouchableHighlight';
export default (MemoedTouchableHighlight: React.AbstractComponent<Props, React.ElementRef<typeof View>>);