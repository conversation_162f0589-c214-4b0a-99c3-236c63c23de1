import { createClient } from '@supabase/supabase-js';

// Replace with your Supabase project URL and anon key
const supabaseUrl = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpqZ3p4c3htZmVhd2tqeW1vbmlnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg5MDMzMTAsImV4cCI6MjA2NDQ3OTMxMH0.Up6MNAuAdnAqRX2Ar3lRA0_PFmfF6Aq3f9btc3xRTlI';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpqZ3p4c3htZmVhd2tqeW1vbmlnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODkwMzMxMCwiZXhwIjoyMDY0NDc5MzEwfQ.X5i5biHa1rz06Sw0fWjQM-ce7T93DyI8KiYABlGFCwI';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// =============================================================================
// DIRECT SUPABASE AUTH (Secure & Required)
// =============================================================================

export const auth = {
  async signUp(email, password, userData = {}) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData, // Additional user metadata
      },
    });
    return { data, error };
  },

  async signIn(email, password) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  },

  async signOut() {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  },

  async getSession() {
    const { data: { session } } = await supabase.auth.getSession();
    return session;
  },

  async refreshSession() {
    const { data, error } = await supabase.auth.refreshSession();
    return { data, error };
  },

  // Listen to auth changes
  onAuthStateChange(callback) {
    return supabase.auth.onAuthStateChange(callback);
  },
};

// =============================================================================
// DIRECT SUPABASE DATABASE CALLS (Basic Data)
// =============================================================================

export const database = {
  // Categories - Simple read operations
  async getCategories() {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('active', true)
      .order('name');
    return { data, error };
  },

  // Services - Basic CRUD operations
  async getServices(filters = {}) {
    let query = supabase
      .from('services')
      .select(`
        *,
        categories(name, icon),
        service_providers(name, rating, verified)
      `)
      .eq('active', true);

    if (filters.category_id) {
      query = query.eq('category_id', filters.category_id);
    }
    if (filters.location) {
      // Add location-based filtering later with PostGIS
    }

    const { data, error } = await query.order('created_at', { ascending: false });
    return { data, error };
  },

  async getServiceById(serviceId) {
    const { data, error } = await supabase
      .from('services')
      .select(`
        *,
        categories(name, icon),
        service_providers(
          name,
          rating,
          verified,
          description,
          contact_info
        )
      `)
      .eq('id', serviceId)
      .single();
    return { data, error };
  },

  async getServicesByCategory(categoryId) {
    const { data, error } = await supabase
      .from('services')
      .select(`
        *,
        service_providers(name, rating, verified)
      `)
      .eq('category_id', categoryId)
      .eq('active', true)
      .order('rating', { ascending: false });
    return { data, error };
  },

  // Reviews - Read operations
  async getServiceReviews(serviceId) {
    const { data, error } = await supabase
      .from('reviews')
      .select(`
        *,
        profiles(first_name, avatar_url)
      `)
      .eq('service_id', serviceId)
      .order('created_at', { ascending: false });
    return { data, error };
  },

  // User Profile - Basic operations
  async getUserProfile(userId) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
    return { data, error };
  },

  async updateUserProfile(userId, profileData) {
    const { data, error } = await supabase
      .from('profiles')
      .update(profileData)
      .eq('id', userId)
      .select()
      .single();
    return { data, error };
  },

  // Search functionality
  async searchServices(query) {
    const { data, error } = await supabase
      .from('services')
      .select(`
        *,
        categories(name, icon),
        service_providers(name, rating, verified)
      `)
      .or(`title.ilike.%${query}%, description.ilike.%${query}%`)
      .eq('active', true)
      .order('rating', { ascending: false });
    return { data, error };
  },
};

// =============================================================================
// SUPABASE EDGE FUNCTIONS (Business Logic & Sensitive Operations)
// =============================================================================

export const edgeFunctions = {
  // Bookings - Complex business logic through Edge Functions
  async createBooking(bookingData) {
    const { data, error } = await supabase.functions.invoke('create-booking', {
      body: {
        service_id: bookingData.service_id,
        scheduled_date: bookingData.scheduled_date,
        scheduled_time: bookingData.scheduled_time,
        service_address: bookingData.service_address,
        notes: bookingData.notes,
        estimated_duration: bookingData.estimated_duration,
      },
    });
    return { data, error };
  },

  async getUserBookings() {
    const { data, error } = await supabase.functions.invoke('get-user-bookings');
    return { data, error };
  },

  async updateBooking(bookingId, updateData) {
    const { data, error } = await supabase.functions.invoke('update-booking', {
      body: { booking_id: bookingId, ...updateData },
    });
    return { data, error };
  },

  async cancelBooking(bookingId, reason = null) {
    const { data, error } = await supabase.functions.invoke('cancel-booking', {
      body: { booking_id: bookingId, cancellation_reason: reason },
    });
    return { data, error };
  },

  // Payment processing - Sensitive operations
  async processPayment(bookingId, paymentMethod) {
    const { data, error } = await supabase.functions.invoke('process-payment', {
      body: {
        booking_id: bookingId,
        payment_method: paymentMethod,
      },
    });
    return { data, error };
  },

  async refundPayment(bookingId, amount = null) {
    const { data, error } = await supabase.functions.invoke('refund-payment', {
      body: { booking_id: bookingId, refund_amount: amount },
    });
    return { data, error };
  },

  // Service Provider operations
  async requestServiceProvider(serviceId, requirements) {
    const { data, error } = await supabase.functions.invoke('request-service-provider', {
      body: { service_id: serviceId, requirements },
    });
    return { data, error };
  },

  // Notifications - Complex logic
  async sendNotification(userId, type, data) {
    const { data: result, error } = await supabase.functions.invoke('send-notification', {
      body: { user_id: userId, notification_type: type, data },
    });
    return { data: result, error };
  },

  // Reviews - Business logic validation
  async createReview(serviceId, bookingId, rating, comment) {
    const { data, error } = await supabase.functions.invoke('create-review', {
      body: {
        service_id: serviceId,
        booking_id: bookingId,
        rating,
        comment,
      },
    });
    return { data, error };
  },

  // Location-based services (for future mapping integration)
  async searchServicesByLocation(latitude, longitude, radius = 10, serviceType = null) {
    const { data, error } = await supabase.functions.invoke('search-services-by-location', {
      body: {
        latitude,
        longitude,
        radius,
        service_type: serviceType,
      },
    });
    return { data, error };
  },

  async calculateServiceDistance(userLocation, serviceProviderLocation) {
    const { data, error } = await supabase.functions.invoke('calculate-distance', {
      body: {
        user_location: userLocation,
        provider_location: serviceProviderLocation,
      },
    });
    return { data, error };
  },

  // Analytics and reporting
  async getServiceAnalytics(serviceId) {
    const { data, error } = await supabase.functions.invoke('get-service-analytics', {
      body: { service_id: serviceId },
    });
    return { data, error };
  },

  // Admin operations (if user has admin role)
  async approveServiceProvider(providerId) {
    const { data, error } = await supabase.functions.invoke('approve-service-provider', {
      body: { provider_id: providerId },
    });
    return { data, error };
  },
};

// =============================================================================
// UTILITY FUNCTIONS FOR REACT NATIVE
// =============================================================================

export const storage = {
  async setItem(key, value) {
    try {
      const AsyncStorage = await import('@react-native-async-storage/async-storage');
      await AsyncStorage.default.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Storage setItem error:', error);
    }
  },

  async getItem(key) {
    try {
      const AsyncStorage = await import('@react-native-async-storage/async-storage');
      const value = await AsyncStorage.default.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Storage getItem error:', error);
      return null;
    }
  },

  async removeItem(key) {
    try {
      const AsyncStorage = await import('@react-native-async-storage/async-storage');
      await AsyncStorage.default.removeItem(key);
    } catch (error) {
      console.error('Storage removeItem error:', error);
    }
  },
};

// Storage keys for the app
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  USER_LOCATION: 'user_location',
  RECENT_SEARCHES: 'recent_searches',
  FAVORITE_SERVICES: 'favorite_services',
  APP_SETTINGS: 'app_settings',
};

// =============================================================================
// REAL-TIME SUBSCRIPTIONS
// =============================================================================

export const realtime = {
  // Subscribe to booking updates
  subscribeToBookingUpdates(userId, callback) {
    return supabase
      .channel('booking-updates')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bookings',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  },

  // Subscribe to notifications
  subscribeToNotifications(userId, callback) {
    return supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  },

  // Unsubscribe from channel
  unsubscribe(subscription) {
    return supabase.removeChannel(subscription);
  },
};
