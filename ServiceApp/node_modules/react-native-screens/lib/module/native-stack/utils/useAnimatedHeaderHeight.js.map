{"version": 3, "names": ["React", "AnimatedHeaderHeightContext", "useAnimatedHeaderHeight", "animatedValue", "useContext", "undefined", "Error"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/useAnimatedHeaderHeight.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,2BAA2B,MAAM,+BAA+B;AAEvE,eAAe,SAASC,uBAAuBA,CAAA,EAAG;EAChD,MAAMC,aAAa,GAAGH,KAAK,CAACI,UAAU,CAACH,2BAA2B,CAAC;EAEnE,IAAIE,aAAa,KAAKE,SAAS,EAAE;IAC/B,MAAM,IAAIC,KAAK,CACb,wFACF,CAAC;EACH;EAEA,OAAOH,aAAa;AACtB", "ignoreList": []}