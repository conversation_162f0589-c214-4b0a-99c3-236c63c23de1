/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

'use client';

import type { EventSubscription } from '../../vendor/react-native/vendor/emitter/EventEmitter';
import invariant from 'fbjs/lib/invariant';
import canUseDOM from '../../modules/canUseDom';
export type DisplayMetrics = {|
  fontScale: number,
  height: number,
  scale: number,
  width: number,
|};
type DimensionsValue = {|
  window: DisplayMetrics,
  screen: DisplayMetrics,
|};
type DimensionKey = 'window' | 'screen';
type DimensionEventListenerType = 'change';
const dimensions = {
  window: {
    fontScale: 1,
    height: 0,
    scale: 1,
    width: 0
  },
  screen: {
    fontScale: 1,
    height: 0,
    scale: 1,
    width: 0
  }
};
const listeners = {};
let shouldInit = canUseDOM;
declare function update(): any;
declare function handleResize(): any;
declare export default class Dimensions {
  static get(dimension: DimensionKey): DisplayMetrics,
  static set(initialDimensions: ?DimensionsValue): void,
  static addEventListener(type: DimensionEventListenerType, handler: (DimensionsValue) => void): EventSubscription,
  static removeEventListener(type: DimensionEventListenerType, handler: (DimensionsValue) => void): void,
}
if (canUseDOM) {
  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', handleResize, false);
  } else {
    window.addEventListener('resize', handleResize, false);
  }
}