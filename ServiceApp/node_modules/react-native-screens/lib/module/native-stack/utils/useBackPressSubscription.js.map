{"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON>", "useBackPressSubscription", "onBackPress", "isDisabled", "isActive", "setIsActive", "useState", "subscription", "useRef", "clearSubscription", "useCallback", "shouldSetActive", "current", "remove", "undefined", "createSubscription", "addEventListener", "handleAttached", "handleDetached", "useEffect"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/useBackPressSubscription.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAiC,cAAc;AAcnE;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAAC;EACvCC,WAAW;EACXC;AACI,CAAC,EAA4B;EACjC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGN,KAAK,CAACO,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMC,YAAY,GAAGR,KAAK,CAACS,MAAM,CAAsC,CAAC;EAExE,MAAMC,iBAAiB,GAAGV,KAAK,CAACW,WAAW,CAAC,CAACC,eAAe,GAAG,IAAI,KAAK;IACtEJ,YAAY,CAACK,OAAO,EAAEC,MAAM,CAAC,CAAC;IAC9BN,YAAY,CAACK,OAAO,GAAGE,SAAS;IAChC,IAAIH,eAAe,EAAEN,WAAW,CAAC,KAAK,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,kBAAkB,GAAGhB,KAAK,CAACW,WAAW,CAAC,MAAM;IACjD,IAAI,CAACP,UAAU,EAAE;MACfI,YAAY,CAACK,OAAO,EAAEC,MAAM,CAAC,CAAC;MAC9BN,YAAY,CAACK,OAAO,GAAGZ,WAAW,CAACgB,gBAAgB,CACjD,mBAAmB,EACnBd,WACF,CAAC;MACDG,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,CAACF,UAAU,EAAED,WAAW,CAAC,CAAC;EAE7B,MAAMe,cAAc,GAAGlB,KAAK,CAACW,WAAW,CAAC,MAAM;IAC7C,IAAIN,QAAQ,EAAE;MACZW,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,kBAAkB,EAAEX,QAAQ,CAAC,CAAC;EAElC,MAAMc,cAAc,GAAGnB,KAAK,CAACW,WAAW,CAAC,MAAM;IAC7CD,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvBV,KAAK,CAACoB,SAAS,CAAC,MAAM;IACpB,IAAIhB,UAAU,EAAE;MACdM,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACN,UAAU,EAAEM,iBAAiB,CAAC,CAAC;EAEnC,OAAO;IACLQ,cAAc;IACdC,cAAc;IACdH,kBAAkB;IAClBN;EACF,CAAC;AACH", "ignoreList": []}