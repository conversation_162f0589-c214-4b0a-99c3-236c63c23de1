/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

import { AnimatedEvent } from '../AnimatedEvent';
import AnimatedNode from './AnimatedNode';
import AnimatedStyle from './AnimatedStyle';
import NativeAnimatedHelper from '../NativeAnimatedHelper';
import invariant from 'fbjs/lib/invariant';
declare class AnimatedProps extends AnimatedNode {
  _props: Object,
  _animatedView: any,
  _callback: () => void,
  constructor(props: Object, callback: () => void): any,
  __getValue(): Object,
  __getAnimatedValue(): Object,
  __attach(): void,
  __detach(): void,
  update(): void,
  __makeNative(): void,
  setNativeView(animatedView: any): void,
  __connectAnimatedView(): void,
  __disconnectAnimatedView(): void,
  __restoreDefaultValues(): void,
  __getNativeConfig(): Object,
}
export default AnimatedProps;