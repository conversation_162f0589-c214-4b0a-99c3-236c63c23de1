/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import invariant from 'fbjs/lib/invariant';
import canUseDOM from '../../modules/canUseDom';
const initialURL = canUseDOM ? window.location.href : '';
type Callback = (...args: any) => void;
declare class Linking {
  _eventCallbacks: {
    [key: string]: Array<Callback>
  },
  _dispatchEvent(event: string, ...data: any): any,
  addEventListener(eventType: string, callback: Callback): {|
    remove(): void
  |},
  removeEventListener(eventType: string, callback: Callback): void,
  canOpenURL(): Promise<boolean>,
  getInitialURL(): Promise<string>,
  openURL(url: string, target?: string): Promise<Object | void>,
  _validateURL(url: string): any,
}
declare var open: (url: any, target: any) => any;
export default (new Linking(): Linking);