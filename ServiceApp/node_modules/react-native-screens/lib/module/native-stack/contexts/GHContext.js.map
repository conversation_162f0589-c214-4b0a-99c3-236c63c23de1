{"version": 3, "names": ["React", "GHContext", "createContext", "props", "createElement", "Fragment", "children"], "sourceRoot": "../../../../src", "sources": ["native-stack/contexts/GHContext.tsx"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAA6B,OAAO;AAGhD;AACA,OAAO,MAAMC,SAAS,gBAAGD,KAAK,CAACE,aAAa,CACzCC,KAA8C,iBAAKH,KAAA,CAAAI,aAAA,CAAAJ,KAAA,CAAAK,QAAA,QAAGF,KAAK,CAACG,QAAW,CAC1E,CAAC", "ignoreList": []}