import React, { useContext, useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { AuthContext } from '../context/AuthContext';
import { database } from '../supabase/client';
import Header from '../components/Header';
import ServiceCard from '../components/ServiceCard';
import SupabaseTest from '../components/SupabaseTest';

export default function HomeScreen({ navigation }) {
  const { user } = useContext(AuthContext);
  const [categories, setCategories] = useState([]);
  const [featuredServices, setFeaturedServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showSupabaseTest, setShowSupabaseTest] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      const { data: categoriesData, error: categoriesError } = await database.getCategories();
      if (categoriesError) {
        console.error('Error loading categories:', categoriesError);
      } else {
        setCategories(categoriesData || []);
      }

      const { data: servicesData, error: servicesError } = await database.getServices({ limit: 5 });
      if (servicesError) {
        console.error('Error loading services:', servicesError);
      } else {
        setFeaturedServices(servicesData || []);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading services...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header title="Home Services" />

      <ScrollView style={styles.content}>
        <Text style={styles.welcomeText}>
          Welcome back, {user?.email}!
        </Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.categoriesContainer}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={styles.categoryCard}
                  onPress={() => navigation.navigate('Category', { category })}
                >
                  <View style={styles.iconCircle}>
                    <Text style={styles.categoryIcon}>{category.icon}</Text>
                  </View>
                  <Text style={styles.categoryName}>{category.name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Featured Services</Text>
          {featuredServices.map((service) => (
            <View key={service.id} style={styles.serviceCardWrapper}>
              <ServiceCard
                service={{
                  ...service,
                  provider: service.service_providers?.name || 'Service Provider',
                  rating: service.service_providers?.rating || 0,
                  price: service.price_per_hour ? `$${service.price_per_hour}/hour` : 'Contact for pricing',
                }}
                onPress={() => navigation.navigate('Service', { service })}
              />
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Supabase Test Button */}
      <TouchableOpacity
        style={styles.testButton}
        onPress={() => setShowSupabaseTest(true)}
      >
        <Text style={styles.testButtonText}>🔧 Test Supabase</Text>
      </TouchableOpacity>

      {/* Supabase Test Modal */}
      <Modal
        visible={showSupabaseTest}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowSupabaseTest(false)}
              style={styles.closeButton}
            >
              <Text style={styles.closeButtonText}>✕ Close</Text>
            </TouchableOpacity>
          </View>
          <SupabaseTest />
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fafafa', // light gray background like Booksy
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  welcomeText: {
    fontSize: 18,
    marginBottom: 20,
    color: '#333',
    fontWeight: '500',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#222',
  },
  categoriesContainer: {
    flexDirection: 'row',
    paddingBottom: 10,
  },
  categoryCard: {
    alignItems: 'center',
    marginRight: 15,
    padding: 10,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 3,
    width: 80,
  },
  iconCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
  },
  categoryIcon: {
    fontSize: 24,
  },
  categoryName: {
    fontSize: 12,
    textAlign: 'center',
    color: '#333',
  },
  serviceCardWrapper: {
    marginBottom: 12, // spacing between service cards
  },
  testButton: {
    position: 'absolute',
    bottom: 100,
    right: 20,
    backgroundColor: '#007AFF',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  testButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  closeButton: {
    padding: 10,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
});
