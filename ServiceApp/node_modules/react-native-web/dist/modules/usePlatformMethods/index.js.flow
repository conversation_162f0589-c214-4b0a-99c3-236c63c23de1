/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import type { GenericStyleProp } from '../../types';
import type { ViewProps } from '../../exports/View';
import UIManager from '../../exports/UIManager';
import useStable from '../useStable';

/**
 * Adds non-standard methods to the hode element. This is temporarily until an
 * API like `ReactNative.measure(hostRef, callback)` is added to React Native.
 */
declare export default function usePlatformMethods(arg0: {
  style?: GenericStyleProp<*>,
  pointerEvents?: $PropertyType<ViewProps, 'pointerEvents'>,
}): (hostNode: any) => void;