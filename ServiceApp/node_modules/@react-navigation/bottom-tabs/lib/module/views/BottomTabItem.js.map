{"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "Label", "PlatformPressable", "useTheme", "Color", "React", "Platform", "StyleSheet", "View", "TabBarIcon", "jsx", "_jsx", "jsxs", "_jsxs", "renderButtonDefault", "props", "SUPPORTS_LARGE_CONTENT_VIEWER", "OS", "parseInt", "Version", "BottomTabItem", "route", "href", "focused", "descriptor", "label", "icon", "badge", "badgeStyle", "button", "accessibilityLabel", "testID", "onPress", "onLongPress", "horizontal", "compact", "sidebar", "variant", "activeTintColor", "customActiveTintColor", "inactiveTintColor", "customInactiveTintColor", "activeBackgroundColor", "customActiveBackgroundColor", "inactiveBackgroundColor", "showLabel", "allowFontScaling", "undefined", "labelStyle", "iconStyle", "style", "colors", "fonts", "primary", "isDark", "darken", "string", "text", "alpha", "rgb", "mix", "card", "hex", "options", "labelString", "tabBarLabel", "title", "name", "labelInactiveTintColor", "iconInactiveTintColor", "renderLabel", "color", "position", "children", "styles", "labelBeside", "labelSidebarMaterial", "labelSidebarUiKit", "labelBesideUikitCompact", "labelBesideUikit", "marginStart", "labelBeneath", "regular", "medium", "tintColor", "renderIcon", "activeOpacity", "inactiveOpacity", "size", "scene", "backgroundColor", "flex", "flatten", "borderRadius", "overflow", "select", "ios", "default", "borderless", "tab", "tabBarSidebarMaterial", "tabVerticalMaterial", "tabBarSidebarUiKit", "tabVerticalUiKit", "tabHorizontalUiKit", "Fragment", "create", "alignItems", "justifyContent", "flexDirection", "padding", "paddingVertical", "paddingHorizontal", "paddingStart", "paddingEnd", "fontSize", "marginEnd", "lineHeight"], "sourceRoot": "../../../src", "sources": ["views/BottomTabItem.tsx"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,KAAK,EAAEC,iBAAiB,QAAQ,4BAA4B;AAC/E,SAAqBC,QAAQ,QAAQ,0BAA0B;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,OAAO;AACzB,SAEEC,QAAQ,EAERC,UAAU,EAEVC,IAAI,QAEC,cAAc;AAOrB,SAASC,UAAU,QAAQ,iBAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AA8H1C,MAAMC,mBAAmB,GAAIC,KAA8B,iBACzDJ,IAAA,CAACT,iBAAiB;EAAA,GAAKa;AAAK,CAAG,CAChC;AAED,MAAMC,6BAA6B,GACjCV,QAAQ,CAACW,EAAE,KAAK,KAAK,IAAIC,QAAQ,CAACZ,QAAQ,CAACa,OAAO,EAAE,EAAE,CAAC,IAAI,EAAE;AAE/D,OAAO,SAASC,aAAaA,CAAC;EAC5BC,KAAK;EACLC,IAAI;EACJC,OAAO;EACPC,UAAU;EACVC,KAAK;EACLC,IAAI;EACJC,KAAK;EACLC,UAAU;EACVC,MAAM,GAAGf,mBAAmB;EAC5BgB,kBAAkB;EAClBC,MAAM;EACNC,OAAO;EACPC,WAAW;EACXC,UAAU;EACVC,OAAO;EACPC,OAAO;EACPC,OAAO;EACPC,eAAe,EAAEC,qBAAqB;EACtCC,iBAAiB,EAAEC,uBAAuB;EAC1CC,qBAAqB,EAAEC,2BAA2B;EAClDC,uBAAuB,GAAG,aAAa;EACvCC,SAAS,GAAG,IAAI;EAChB;EACA;EACA;EACAC,gBAAgB,GAAG9B,6BAA6B,GAAG,KAAK,GAAG+B,SAAS;EACpEC,UAAU;EACVC,SAAS;EACTC;AACK,CAAC,EAAE;EACR,MAAM;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAGjD,QAAQ,CAAC,CAAC;EAEpC,MAAMmC,eAAe,GACnBC,qBAAqB,KACpBF,OAAO,KAAK,OAAO,IAAID,OAAO,IAAIF,UAAU,GACzC9B,KAAK,CAAC+C,MAAM,CAACE,OAAO,CAAC,CAACC,MAAM,CAAC,CAAC,GAC5B,OAAO,GACPlD,KAAK,CAAC+C,MAAM,CAACE,OAAO,CAAC,CAACE,MAAM,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,CAAC,GAC7CL,MAAM,CAACE,OAAO,CAAC;EAErB,MAAMb,iBAAiB,GACrBC,uBAAuB,KAAKM,SAAS,GACjCV,OAAO,KAAK,UAAU,GACpBjC,KAAK,CAAC+C,MAAM,CAACM,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACH,MAAM,CAAC,CAAC,GAC7CpD,KAAK,CAAC+C,MAAM,CAACM,IAAI,CAAC,CAACG,GAAG,CAACxD,KAAK,CAAC+C,MAAM,CAACU,IAAI,CAAC,EAAE,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,GACvDrB,uBAAuB;EAE7B,MAAMC,qBAAqB,GACzBC,2BAA2B,KAC1BN,OAAO,KAAK,UAAU,GACnBjC,KAAK,CAACkC,eAAe,CAAC,CAACoB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACH,MAAM,CAAC,CAAC,GACjDpB,OAAO,IAAIF,UAAU,GACnBiB,MAAM,CAACE,OAAO,GACd,aAAa,CAAC;EAEtB,MAAM;IAAEU;EAAQ,CAAC,GAAGvC,UAAU;EAC9B,MAAMwC,WAAW,GAAGhE,QAAQ,CAC1B;IACEyB,KAAK,EACH,OAAOsC,OAAO,CAACE,WAAW,KAAK,QAAQ,GACnCF,OAAO,CAACE,WAAW,GACnBlB,SAAS;IACfmB,KAAK,EAAEH,OAAO,CAACG;EACjB,CAAC,EACD7C,KAAK,CAAC8C,IACR,CAAC;EAED,IAAIC,sBAAsB,GAAG5B,iBAAiB;EAC9C,IAAI6B,qBAAqB,GAAG7B,iBAAiB;EAE7C,IACEH,OAAO,KAAK,OAAO,IACnBD,OAAO,IACPF,UAAU,IACVO,uBAAuB,KAAKM,SAAS,EACrC;IACAsB,qBAAqB,GAAGlB,MAAM,CAACE,OAAO;IACtCe,sBAAsB,GAAGjB,MAAM,CAACM,IAAI;EACtC;EAEA,MAAMa,WAAW,GAAGA,CAAC;IAAE/C;EAA8B,CAAC,KAAK;IACzD,IAAIsB,SAAS,KAAK,KAAK,EAAE;MACvB,OAAO,IAAI;IACb;IAEA,MAAM0B,KAAK,GAAGhD,OAAO,GAAGe,eAAe,GAAG8B,sBAAsB;IAEhE,IAAI,OAAO3C,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK,CAAC;QACXF,OAAO;QACPgD,KAAK;QACLC,QAAQ,EAAEtC,UAAU,GAAG,aAAa,GAAG,YAAY;QACnDuC,QAAQ,EAAET;MACZ,CAAC,CAAC;IACJ;IAEA,oBACErD,IAAA,CAACV,KAAK;MACJiD,KAAK,EAAE,CACLhB,UAAU,GACN,CACEwC,MAAM,CAACC,WAAW,EAClBtC,OAAO,KAAK,UAAU,GAClBqC,MAAM,CAACE,oBAAoB,GAC3BxC,OAAO,GACLsC,MAAM,CAACG,iBAAiB,GACxB1C,OAAO,GACLuC,MAAM,CAACI,uBAAuB,GAC9BJ,MAAM,CAACK,gBAAgB,EAC/BrD,IAAI,IAAI,IAAI,IAAI;QAAEsD,WAAW,EAAE;MAAE,CAAC,CACnC,GACDN,MAAM,CAACO,YAAY,EACvB9C,OAAO,IAAKE,OAAO,KAAK,OAAO,IAAID,OAAO,IAAIF,UAAW,GACrDkB,KAAK,CAAC8B,OAAO,GACb9B,KAAK,CAAC+B,MAAM,EAChBnC,UAAU,CACV;MACFF,gBAAgB,EAAEA,gBAAiB;MACnCsC,SAAS,EAAEb,KAAM;MAAAE,QAAA,EAEhBhD;IAAK,CACD,CAAC;EAEZ,CAAC;EAED,MAAM4D,UAAU,GAAGA,CAAC;IAAE9D;EAA8B,CAAC,KAAK;IACxD,IAAIG,IAAI,KAAKqB,SAAS,EAAE;MACtB,OAAO,IAAI;IACb;IAEA,MAAMuC,aAAa,GAAG/D,OAAO,GAAG,CAAC,GAAG,CAAC;IACrC,MAAMgE,eAAe,GAAGhE,OAAO,GAAG,CAAC,GAAG,CAAC;IAEvC,oBACEZ,IAAA,CAACF,UAAU;MACTY,KAAK,EAAEA,KAAM;MACbgB,OAAO,EAAEA,OAAQ;MACjBmD,IAAI,EAAErD,OAAO,GAAG,SAAS,GAAG,SAAU;MACtCR,KAAK,EAAEA,KAAM;MACbC,UAAU,EAAEA,UAAW;MACvB0D,aAAa,EAAEA,aAAc;MAC7BxC,gBAAgB,EAAEA,gBAAiB;MACnCyC,eAAe,EAAEA,eAAgB;MACjCjD,eAAe,EAAEA,eAAgB;MACjCE,iBAAiB,EAAE6B,qBAAsB;MACzCgB,UAAU,EAAE3D,IAAK;MACjBwB,KAAK,EAAED;IAAU,CAClB,CAAC;EAEN,CAAC;EAED,MAAMwC,KAAK,GAAG;IAAEpE,KAAK;IAAEE;EAAQ,CAAC;EAEhC,MAAMmE,eAAe,GAAGnE,OAAO,GAC3BmB,qBAAqB,GACrBE,uBAAuB;EAE3B,MAAM;IAAE+C;EAAK,CAAC,GAAGpF,UAAU,CAACqF,OAAO,CAAC1C,KAAK,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM2C,YAAY,GAChBxD,OAAO,KAAK,UAAU,GAClBH,UAAU,GACR,EAAE,GACF,EAAE,GACJE,OAAO,IAAIF,UAAU,GACnB,EAAE,GACF,CAAC;EAET,oBACEvB,IAAA,CAACH,IAAI;IACH0C,KAAK,EAAE;IACL;IACA;MACE2C,YAAY;MACZC,QAAQ,EAAEzD,OAAO,KAAK,UAAU,GAAG,QAAQ,GAAG;IAChD,CAAC,EACDa,KAAK,CACL;IAAAuB,QAAA,EAED5C,MAAM,CAAC;MACNP,IAAI;MACJU,OAAO;MACPC,WAAW;MACXF,MAAM;MACN,YAAY,EAAED,kBAAkB;MAChC,gCAAgC,EAAEkC,WAAW;MAC7C,sCAAsC,EAAE,IAAI;MAC5C;MACA,MAAM,EAAE1D,QAAQ,CAACyF,MAAM,CAAC;QAAEC,GAAG,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MAC1D,eAAe,EAAE1E,OAAO;MACxB,gBAAgB,EAAE;QAAE2E,UAAU,EAAE;MAAK,CAAC;MACtC,aAAa,EACX7D,OAAO,KAAK,UAAU,IAAKD,OAAO,IAAIF,UAAW,GAC7C;QAAEqC,KAAK,EAAEpB,MAAM,CAACM;MAAK,CAAC,GACtBV,SAAS;MACf,cAAc,EAAE,CAAC;MACjB,OAAO,EAAE,CACP2B,MAAM,CAACyB,GAAG,EACV;QAAER,IAAI;QAAED,eAAe;QAAEG;MAAa,CAAC,EACvCzD,OAAO,GACHC,OAAO,KAAK,UAAU,GACpBH,UAAU,GACRwC,MAAM,CAAC0B,qBAAqB,GAC5B1B,MAAM,CAAC2B,mBAAmB,GAC5BnE,UAAU,GACRwC,MAAM,CAAC4B,kBAAkB,GACzB5B,MAAM,CAAC6B,gBAAgB,GAC3BlE,OAAO,KAAK,UAAU,GACpBqC,MAAM,CAAC2B,mBAAmB,GAC1BnE,UAAU,GACRwC,MAAM,CAAC8B,kBAAkB,GACzB9B,MAAM,CAAC6B,gBAAgB,CAChC;MACD,UAAU,eACR1F,KAAA,CAACR,KAAK,CAACoG,QAAQ;QAAAhC,QAAA,GACZY,UAAU,CAACI,KAAK,CAAC,EACjBnB,WAAW,CAACmB,KAAK,CAAC;MAAA,CACL;IAEpB,CAAC;EAAC,CACE,CAAC;AAEX;AAEA,MAAMf,MAAM,GAAGnE,UAAU,CAACmG,MAAM,CAAC;EAC/BP,GAAG,EAAE;IACHQ,UAAU,EAAE,QAAQ;IACpB;IACAd,YAAY,EAAE;EAChB,CAAC;EACDU,gBAAgB,EAAE;IAChBK,cAAc,EAAE,YAAY;IAC5BC,aAAa,EAAE,QAAQ;IACvBC,OAAO,EAAE;EACX,CAAC;EACDT,mBAAmB,EAAE;IACnBS,OAAO,EAAE;EACX,CAAC;EACDN,kBAAkB,EAAE;IAClBI,cAAc,EAAE,QAAQ;IACxBD,UAAU,EAAE,QAAQ;IACpBE,aAAa,EAAE,KAAK;IACpBC,OAAO,EAAE;EACX,CAAC;EACDR,kBAAkB,EAAE;IAClBM,cAAc,EAAE,YAAY;IAC5BD,UAAU,EAAE,QAAQ;IACpBE,aAAa,EAAE,KAAK;IACpBE,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE;EACrB,CAAC;EACDZ,qBAAqB,EAAE;IACrBQ,cAAc,EAAE,YAAY;IAC5BD,UAAU,EAAE,QAAQ;IACpBE,aAAa,EAAE,KAAK;IACpBE,eAAe,EAAE,EAAE;IACnBE,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE;EACd,CAAC;EACDtC,oBAAoB,EAAE;IACpBI,WAAW,EAAE;EACf,CAAC;EACDH,iBAAiB,EAAE;IACjBsC,QAAQ,EAAE,EAAE;IACZnC,WAAW,EAAE;EACf,CAAC;EACDC,YAAY,EAAE;IACZkC,QAAQ,EAAE;EACZ,CAAC;EACDxC,WAAW,EAAE;IACXyC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE;EACd,CAAC;EACDtC,gBAAgB,EAAE;IAChBoC,QAAQ,EAAE,EAAE;IACZnC,WAAW,EAAE;EACf,CAAC;EACDF,uBAAuB,EAAE;IACvBqC,QAAQ,EAAE,EAAE;IACZnC,WAAW,EAAE;EACf;AACF,CAAC,CAAC", "ignoreList": []}