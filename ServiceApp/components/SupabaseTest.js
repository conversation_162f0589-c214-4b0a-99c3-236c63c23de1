import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { auth, supabase } from '../supabase/client';

export default function SupabaseTest() {
  const [loading, setLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('Not tested');
  const [authStatus, setAuthStatus] = useState('Not tested');

  const testConnection = async () => {
    setLoading(true);
    try {
      // Test basic Supabase connection
      const { data, error } = await supabase
        .from('test')
        .select('*')
        .limit(1);
      
      if (error) {
        // This is expected if table doesn't exist
        if (error.message.includes('relation "public.test" does not exist')) {
          setConnectionStatus('✅ Connected (table not found is expected)');
        } else {
          setConnectionStatus(`❌ Error: ${error.message}`);
        }
      } else {
        setConnectionStatus('✅ Connected successfully');
      }
    } catch (error) {
      setConnectionStatus(`❌ Connection failed: ${error.message}`);
    }
    setLoading(false);
  };

  const testAuth = async () => {
    setLoading(true);
    try {
      // Test auth by getting current session
      const session = await auth.getSession();
      if (session) {
        setAuthStatus('✅ Auth working - Session found');
      } else {
        setAuthStatus('✅ Auth working - No active session');
      }
    } catch (error) {
      setAuthStatus(`❌ Auth error: ${error.message}`);
    }
    setLoading(false);
  };

  const testSignUp = async () => {
    setLoading(true);
    try {
      // Test with a dummy email (this will likely fail but shows auth is working)
      const { data, error } = await auth.signUp('<EMAIL>', 'testpassword123');
      
      if (error) {
        if (error.message.includes('User already registered')) {
          setAuthStatus('✅ Auth working - User exists');
        } else if (error.message.includes('Invalid email')) {
          setAuthStatus('✅ Auth working - Email validation works');
        } else {
          setAuthStatus(`✅ Auth working - ${error.message}`);
        }
      } else {
        setAuthStatus('✅ Auth working - Signup successful');
      }
    } catch (error) {
      setAuthStatus(`❌ Auth signup error: ${error.message}`);
    }
    setLoading(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔧 Supabase Connection Test</Text>
      
      <View style={styles.testSection}>
        <Text style={styles.sectionTitle}>Database Connection</Text>
        <Text style={styles.status}>{connectionStatus}</Text>
        <TouchableOpacity 
          style={styles.button} 
          onPress={testConnection}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Connection</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.testSection}>
        <Text style={styles.sectionTitle}>Authentication</Text>
        <Text style={styles.status}>{authStatus}</Text>
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={[styles.button, styles.smallButton]} 
            onPress={testAuth}
            disabled={loading}
          >
            <Text style={styles.buttonText}>Test Session</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.button, styles.smallButton]} 
            onPress={testSignUp}
            disabled={loading}
          >
            <Text style={styles.buttonText}>Test Signup</Text>
          </TouchableOpacity>
        </View>
      </View>

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Testing...</Text>
        </View>
      )}

      <View style={styles.infoSection}>
        <Text style={styles.infoTitle}>ℹ️ Info</Text>
        <Text style={styles.infoText}>
          This tests your Supabase connection and authentication setup.
          {'\n\n'}
          • Database errors are expected if tables don't exist yet
          • Auth errors help verify the connection is working
          • Green checkmarks mean Supabase is properly configured
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f8f9fa',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  testSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  status: {
    fontSize: 16,
    marginBottom: 15,
    padding: 10,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    color: '#333',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  smallButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  buttonRow: {
    flexDirection: 'row',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  infoSection: {
    backgroundColor: '#e3f2fd',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#1976d2',
  },
  infoText: {
    fontSize: 14,
    color: '#1976d2',
    lineHeight: 20,
  },
});
