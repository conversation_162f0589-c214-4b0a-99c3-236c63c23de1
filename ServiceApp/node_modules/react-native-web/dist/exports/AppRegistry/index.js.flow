/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

'use client';

import type { Application } from './renderApplication';
import type { ComponentType, Node } from 'react';
import invariant from 'fbjs/lib/invariant';
import unmountComponentAtNode from '../unmountComponentAtNode';
import renderApplication, { getApplication } from './renderApplication';
type AppParams = Object;
type Runnable = {|
  getApplication?: (AppParams) => {|
    element: Node,
    getStyleElement: (any) => Node,
  |},
  run: (AppParams) => any,
|};
export type ComponentProvider = () => ComponentType<any>;
export type ComponentProviderInstrumentationHook = (component: ComponentProvider) => ComponentType<any>;
export type WrapperComponentProvider = (any) => ComponentType<*>;
export type AppConfig = {
  appKey: string,
  component?: ComponentProvider,
  run?: Function,
  section?: boolean,
};
const emptyObject = {};
const runnables: {|
  [appKey: string]: Runnable
|} = {};
declare var componentProviderInstrumentationHook: (component: ComponentProvider) => any;
let wrapperComponentProvider: ?WrapperComponentProvider;

/**
 * `AppRegistry` is the JS entry point to running all React Native apps.
 */
declare export default class AppRegistry {
  static getAppKeys(): Array<string>,
  static getApplication(appKey: string, appParameters?: AppParams): {|
    element: Node,
    getStyleElement: (any) => Node,
  |},
  static registerComponent(appKey: string, componentProvider: ComponentProvider): string,
  static registerConfig(config: Array<AppConfig>): any,
  static registerRunnable(appKey: string, run: Function): string,
  static runApplication(appKey: string, appParameters: Object): Application,
  static setComponentProviderInstrumentationHook(hook: ComponentProviderInstrumentationHook): any,
  static setWrapperComponentProvider(provider: WrapperComponentProvider): any,
  static unmountApplicationComponentAtRootTag(rootTag: Object): any,
}